using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace YdPublicFunction.Mdl
{
    /// <summary>
    /// 获取消息读取列表输出模型
    /// </summary>
    public class MdlGetMessageReadListOut
    {
        /// <summary>
        /// 消息ID
        /// </summary>
        public int MessageId { get; set; }

        /// <summary>
        /// 消息标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public string MessageContent { get; set; }

        /// <summary>
        /// 发送者名称
        /// </summary>
        public string SenderName { get; set; }

        /// <summary>
        /// 读取状态
        /// </summary>
        public string ReadStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public string PublishDate { get; set; }

        /// <summary>
        /// 药店代码
        /// </summary>
        public string Yd_Code { get; set; }
    }
}