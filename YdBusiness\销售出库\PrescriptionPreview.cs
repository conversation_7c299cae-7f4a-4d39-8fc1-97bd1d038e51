using System;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace YdBusiness
{
    public partial class PrescriptionPreview : Common.BaseForm.BaseFather
    {
        private byte[] fileData;
        private string fileName;
        private float zoomFactor = 1.0f;
        private Image originalImage;

        public PrescriptionPreview(byte[] prescriptionData, string prescriptionFileName)
        {
            InitializeComponent();
            fileData = prescriptionData;
            fileName = prescriptionFileName;
            LoadPrescriptionFile();
        }

        private void LoadPrescriptionFile()
        {
            try
            {
                if (fileData == null || fileData.Length == 0)
                {
                    MessageBox.Show("没有处方文件数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                LblFileName.Text = $"文件名：{fileName}";
                LblFileSize.Text = $"文件大小：{FormatFileSize(fileData.Length)}";

                string extension = Path.GetExtension(fileName).ToLower();

                if (IsImageFile(extension))
                {
                    LoadImageFile();
                }
                else if (extension == ".pdf")
                {
                    ShowPdfMessage();
                }
                else
                {
                    ShowUnsupportedMessage();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载处方文件时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private bool IsImageFile(string extension)
        {
            string[] imageExtensions = { ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".tif" };
            foreach (string ext in imageExtensions)
            {
                if (extension == ext)
                    return true;
            }
            return false;
        }

        private void LoadImageFile()
        {
            try
            {
                using (MemoryStream ms = new MemoryStream(fileData))
                {
                    originalImage = Image.FromStream(ms);
                    pictureBox1.Image = originalImage;
                    pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"无法加载图片文件：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowPdfMessage()
        {
            pictureBox1.Image = null;
            // 创建一个简单的文本图片显示PDF信息
            Bitmap bitmap = new Bitmap(400, 200);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.White);
                Font font = new Font("宋体", 12);
                string message = "PDF文件预览\n\n" +
                               $"文件名：{fileName}\n" +
                               $"文件大小：{FormatFileSize(fileData.Length)}\n\n" +
                               "请点击\"另存为\"按钮保存文件后\n" +
                               "使用PDF阅读器打开查看";

                g.DrawString(message, font, Brushes.Black, new RectangleF(10, 10, 380, 180));
            }
            pictureBox1.Image = bitmap;
            pictureBox1.SizeMode = PictureBoxSizeMode.CenterImage;
        }

        private void ShowUnsupportedMessage()
        {
            pictureBox1.Image = null;
            // 创建一个简单的文本图片显示不支持的文件类型信息
            Bitmap bitmap = new Bitmap(400, 200);
            using (Graphics g = Graphics.FromImage(bitmap))
            {
                g.Clear(Color.White);
                Font font = new Font("宋体", 12);
                string message = "不支持的文件类型\n\n" +
                               $"文件名：{fileName}\n" +
                               $"文件大小：{FormatFileSize(fileData.Length)}\n\n" +
                               "请点击\"另存为\"按钮保存文件后\n" +
                               "使用相应的程序打开查看";

                g.DrawString(message, font, Brushes.Black, new RectangleF(10, 10, 380, 180));
            }
            pictureBox1.Image = bitmap;
            pictureBox1.SizeMode = PictureBoxSizeMode.CenterImage;
        }

        private string FormatFileSize(long bytes)
        {
            if (bytes < 1024)
                return $"{bytes} 字节";
            else if (bytes < 1024 * 1024)
                return $"{bytes / 1024.0:F1} KB";
            else
                return $"{bytes / (1024.0 * 1024.0):F1} MB";
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                using (SaveFileDialog saveFileDialog = new SaveFileDialog())
                {
                    saveFileDialog.FileName = fileName;
                    saveFileDialog.Filter = "所有文件|*.*";
                    saveFileDialog.Title = "另存为";

                    if (saveFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        File.WriteAllBytes(saveFileDialog.FileName, fileData);
                        MessageBox.Show("文件保存成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存文件时发生错误：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnZoomIn_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                zoomFactor *= 1.2f;
                ApplyZoom();
            }
        }

        private void BtnZoomOut_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                zoomFactor /= 1.2f;
                if (zoomFactor < 0.1f) zoomFactor = 0.1f;
                ApplyZoom();
            }
        }

        private void BtnFitWindow_Click(object sender, EventArgs e)
        {
            if (originalImage != null)
            {
                pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
                zoomFactor = 1.0f;
            }
        }

        private void ApplyZoom()
        {
            if (originalImage != null)
            {
                int newWidth = (int)(originalImage.Width * zoomFactor);
                int newHeight = (int)(originalImage.Height * zoomFactor);

                Bitmap zoomedImage = new Bitmap(newWidth, newHeight);
                using (Graphics g = Graphics.FromImage(zoomedImage))
                {
                    g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                    g.DrawImage(originalImage, 0, 0, newWidth, newHeight);
                }

                pictureBox1.SizeMode = PictureBoxSizeMode.CenterImage;
                pictureBox1.Image = zoomedImage;
            }
        }


    }
}
